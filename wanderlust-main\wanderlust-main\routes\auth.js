const express = require("express");
const router = express.Router();
const User = require("../models/user");
const passport = require("passport");

// Sign Up Routes
router.get("/signup", (req, res) => {
  res.render("auth/signup", { 
    layout: false,
    error: req.flash("error"),
    success: req.flash("success")
  });
});

router.post("/signup", async (req, res) => {
  try {
    const { firstName, lastName, email, password, confirmPassword } = req.body;

    console.log("Signup attempt:", { firstName, lastName, email });

    // Validation
    if (!firstName || !lastName || !email || !password) {
      req.flash("error", "All fields are required");
      return res.redirect("/auth/signin#signup");
    }

    if (password !== confirmPassword) {
      req.flash("error", "Passwords do not match");
      return res.redirect("/auth/signin#signup");
    }

    if (password.length < 6) {
      req.flash("error", "Password must be at least 6 characters long");
      return res.redirect("/auth/signin#signup");
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      req.flash("error", "An account with this email already exists");
      return res.redirect("/auth/signin#signup");
    }

    // Create new user
    const newUser = new User({
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.toLowerCase().trim(),
      password: password
    });

    const savedUser = await newUser.save();
    console.log("User created successfully:", savedUser._id);

    req.flash("success", "🎉 Account created successfully! Welcome to Quick Stay! Please sign in to continue.");
    res.redirect("/auth/signin");

  } catch (error) {
    console.error("Signup error:", error);
    req.flash("error", "Something went wrong. Please try again.");
    res.redirect("/auth/signin#signup");
  }
});

// Sign In Routes
router.get("/signin", (req, res) => {
  res.render("auth/signin", { 
    layout: false,
    error: req.flash("error"),
    success: req.flash("success")
  });
});

router.post("/signin", (req, res, next) => {
  passport.authenticate("local", (err, user, info) => {
    if (err) {
      console.error("Authentication error:", err);
      req.flash("error", "Something went wrong. Please try again.");
      return res.redirect("/auth/signin");
    }

    if (!user) {
      req.flash("error", info.message || "Invalid email or password");
      return res.redirect("/auth/signin");
    }

    req.logIn(user, (err) => {
      if (err) {
        console.error("Login error:", err);
        req.flash("error", "Something went wrong. Please try again.");
        return res.redirect("/auth/signin");
      }

      console.log("User signed in successfully:", user.email);
      req.flash("success", `🎉 Welcome back, ${user.firstName}! You're now signed in.`);
      return res.redirect("/listings");
    });
  })(req, res, next);
});

// Sign Out Route
router.get("/signout", (req, res) => {
  const userName = req.user ? req.user.firstName : "User";
  req.logout((err) => {
    if (err) {
      console.error("Logout error:", err);
      req.flash("error", "Error signing out. Please try again.");
    } else {
      req.flash("success", `👋 Goodbye, ${userName}! You have been signed out successfully.`);
    }
    res.redirect("/auth/signin");
  });
});

// Forgot Password Route (placeholder)
router.get("/forgot-password", (req, res) => {
  res.render("auth/forgot-password", { 
    layout: false,
    error: req.flash("error"),
    success: req.flash("success")
  });
});

module.exports = router;
