const express = require("express");
const router = express.Router();
const User = require("../models/user");
const passport = require("passport");

// Sign Up Routes
router.get("/signup", (req, res) => {
  res.render("auth/signup", { 
    layout: false,
    error: req.flash("error"),
    success: req.flash("success")
  });
});

router.post("/signup", async (req, res) => {
  try {
    const { firstName, lastName, email, password, confirmPassword } = req.body;
    
    // Validation
    if (!firstName || !lastName || !email || !password) {
      req.flash("error", "All fields are required");
      return res.redirect("/auth/signup");
    }
    
    if (password !== confirmPassword) {
      req.flash("error", "Passwords do not match");
      return res.redirect("/auth/signup");
    }
    
    if (password.length < 6) {
      req.flash("error", "Password must be at least 6 characters long");
      return res.redirect("/auth/signup");
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      req.flash("error", "An account with this email already exists");
      return res.redirect("/auth/signup");
    }
    
    // Create new user
    const newUser = new User({
      firstName,
      lastName,
      email: email.toLowerCase(),
      password
    });
    
    await newUser.save();
    
    req.flash("success", "Account created successfully! Please sign in.");
    res.redirect("/auth/signin");
    
  } catch (error) {
    console.error("Signup error:", error);
    req.flash("error", "Something went wrong. Please try again.");
    res.redirect("/auth/signup");
  }
});

// Sign In Routes
router.get("/signin", (req, res) => {
  res.render("auth/signin", { 
    layout: false,
    error: req.flash("error"),
    success: req.flash("success")
  });
});

router.post("/signin", passport.authenticate("local", {
  successRedirect: "/listings",
  failureRedirect: "/auth/signin",
  failureFlash: true
}));

// Sign Out Route
router.get("/signout", (req, res) => {
  req.logout((err) => {
    if (err) {
      console.error("Logout error:", err);
    }
    req.flash("success", "You have been signed out successfully");
    res.redirect("/auth/signin");
  });
});

// Forgot Password Route (placeholder)
router.get("/forgot-password", (req, res) => {
  res.render("auth/forgot-password", { 
    layout: false,
    error: req.flash("error"),
    success: req.flash("success")
  });
});

module.exports = router;
