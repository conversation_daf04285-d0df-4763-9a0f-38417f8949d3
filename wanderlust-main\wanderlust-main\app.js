const express = require("express");
const app = express();
const mongoose = require("mongoose");
const Listing = require("./models/listing.js");
const path = require("path");
const methodOverride = require("method-override");
const ejsLayouts = require("express-ejs-layouts");
const session = require("express-session");
const MongoStore = require("connect-mongo");
const passport = require("passport");
const flash = require("connect-flash");

// Import routes
const authRoutes = require("./routes/auth");

const dbUrl = process.env.ATLASDB_URL || "mongodb+srv://shaikabdulla11033:<EMAIL>/quickstay?retryWrites=true&w=majority&appName=Cluster0";

main()
  .then(() => {
    console.log("connected to DB");
  })
  .catch((err) => {
    console.log(err);
  });

async function main() {
  await mongoose.connect(dbUrl);
}

app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));
app.use(ejsLayouts);
app.set("layout", "layouts/boilerplate");
app.use(express.urlencoded({ extended: true }));
app.use(methodOverride("_method"));
app.use(express.static(path.join(__dirname, "public")));

// Session configuration
app.use(session({
  secret: "quickstay-secret-key",
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: dbUrl,
    touchAfter: 24 * 3600 // lazy session update
  }),
  cookie: { maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// Passport configuration
require("./config/passport")(passport);
app.use(passport.initialize());
app.use(passport.session());

// Flash messages
app.use(flash());

// Authentication middleware
function isAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }
  req.flash("error", "Please sign in to access this page");
  res.redirect("/auth/signin");
}

// Global variables for templates
app.use((req, res, next) => {
  res.locals.currentUser = req.user;
  res.locals.error = req.flash("error");
  res.locals.success = req.flash("success");
  next();
});

// Routes
app.use("/auth", authRoutes);

app.get("/", (req, res) => {
  if (req.isAuthenticated()) {
    res.redirect("/listings");
  } else {
    res.redirect("/auth/signin");
  }
});

//Index Route - Protected
app.get("/listings", isAuthenticated, async (req, res) => {
  try {
    const allListings = await Listing.find({});
    res.render("listings/index.ejs", { allListings });
  } catch (error) {
    console.error("Error loading listings:", error);
    req.flash("error", "Error loading listings");
    res.redirect("/auth/signin");
  }
});

//New Route - Protected
app.get("/listings/new", isAuthenticated, (req, res) => {
  res.render("listings/new.ejs");
});

//Show Route - Protected
app.get("/listings/:id", isAuthenticated, async (req, res) => {
  try {
    let { id } = req.params;
    const listing = await Listing.findById(id);
    if (!listing) {
      req.flash("error", "Listing not found");
      return res.redirect("/listings");
    }
    res.render("listings/show.ejs", { listing });
  } catch (error) {
    console.error("Error loading listing:", error);
    req.flash("error", "Error loading listing");
    res.redirect("/listings");
  }
});

//Create Route - Protected
app.post("/listings", isAuthenticated, async (req, res) => {
  try {
    console.log("Create request data:", req.body.listing);
    const newListing = new Listing(req.body.listing);
    await newListing.save();
    console.log("New listing created:", newListing);
    req.flash("success", "Listing created successfully!");
    res.redirect("/listings");
  } catch (error) {
    console.error("Create error:", error);
    req.flash("error", "Error creating listing. Please try again.");
    res.redirect("/listings/new");
  }
});

//Edit Route - Protected
app.get("/listings/:id/edit", isAuthenticated, async (req, res) => {
  try {
    let { id } = req.params;
    const listing = await Listing.findById(id);
    if (!listing) {
      req.flash("error", "Listing not found");
      return res.redirect("/listings");
    }
    res.render("listings/edit.ejs", { listing });
  } catch (error) {
    console.error("Error loading listing for edit:", error);
    req.flash("error", "Error loading listing");
    res.redirect("/listings");
  }
});

//Update Route - Protected
app.put("/listings/:id", isAuthenticated, async (req, res) => {
  try {
    let { id } = req.params;
    console.log("Update request for listing:", id);
    console.log("Update data:", req.body.listing);

    const updatedListing = await Listing.findByIdAndUpdate(id, { ...req.body.listing }, { new: true });
    console.log("Updated listing:", updatedListing);

    if (!updatedListing) {
      req.flash("error", "Listing not found");
      return res.redirect("/listings");
    }

    req.flash("success", "Listing updated successfully!");
    res.redirect(`/listings/${id}`);
  } catch (error) {
    console.error("Update error:", error);
    req.flash("error", "Error updating listing. Please try again.");
    res.redirect(`/listings/${id}/edit`);
  }
});

//Delete Route - Protected
app.delete("/listings/:id", isAuthenticated, async (req, res) => {
  try {
    let { id } = req.params;
    console.log("Delete request for listing:", id);
    let deletedListing = await Listing.findByIdAndDelete(id);
    console.log("Deleted listing:", deletedListing);

    if (!deletedListing) {
      req.flash("error", "Listing not found");
      return res.redirect("/listings");
    }

    req.flash("success", "Listing deleted successfully!");
    res.redirect("/listings");
  } catch (error) {
    console.error("Delete error:", error);
    req.flash("error", "Error deleting listing. Please try again.");
    res.redirect("/listings");
  }
});

// app.get("/testListing", async (req, res) => {
//   let sampleListing = new Listing({
//     title: "My New Villa",
//     description: "By the beach",
//     price: 1200,
//     location: "Calangute, Goa",
//     country: "India",
//   });

//   await sampleListing.save();
//   console.log("sample was saved");
//   res.send("successful testing");
// });

app.listen(8080, () => {
  console.log("server is listening to port 8080");
});
