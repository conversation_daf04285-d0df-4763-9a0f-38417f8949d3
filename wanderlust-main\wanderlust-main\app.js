const express = require("express");
const app = express();
const mongoose = require("mongoose");
const Listing = require("./models/listing.js");
const path = require("path");
const methodOverride = require("method-override");
const ejsLayouts = require("express-ejs-layouts");
const session = require("express-session");
const MongoStore = require("connect-mongo");
const passport = require("passport");
const flash = require("connect-flash");

// Import routes
const authRoutes = require("./routes/auth");

const dbUrl = process.env.ATLASDB_URL || "mongodb+srv://shaikabdulla11033:<EMAIL>/quickstay?retryWrites=true&w=majority&appName=Cluster0";

main()
  .then(() => {
    console.log("connected to DB");
  })
  .catch((err) => {
    console.log(err);
  });

async function main() {
  await mongoose.connect(dbUrl);
}

app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));
app.use(ejsLayouts);
app.set("layout", "layouts/boilerplate");
app.use(express.urlencoded({ extended: true }));
app.use(methodOverride("_method"));
app.use(express.static(path.join(__dirname, "public")));

// Session configuration
app.use(session({
  secret: "quickstay-secret-key",
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: dbUrl,
    touchAfter: 24 * 3600 // lazy session update
  }),
  cookie: { maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// Passport configuration
require("./config/passport")(passport);
app.use(passport.initialize());
app.use(passport.session());

// Flash messages
app.use(flash());

// Global variables for templates
app.use((req, res, next) => {
  res.locals.currentUser = req.user;
  res.locals.error = req.flash("error");
  res.locals.success = req.flash("success");
  next();
});

// Routes
app.use("/auth", authRoutes);

app.get("/", (req, res) => {
  res.redirect("/listings");
});

//Index Route
app.get("/listings", async (req, res) => {
  const allListings = await Listing.find({});
  res.render("listings/index.ejs", { allListings });
});

//New Route
app.get("/listings/new", (req, res) => {
  res.render("listings/new.ejs");
});

//Show Route
app.get("/listings/:id", async (req, res) => {
  let { id } = req.params;
  const listing = await Listing.findById(id);
  res.render("listings/show.ejs", { listing });
});

//Create Route
app.post("/listings", async (req, res) => {
  try {
    console.log("Create request data:", req.body.listing);
    const newListing = new Listing(req.body.listing);
    await newListing.save();
    console.log("New listing created:", newListing);
    res.redirect("/listings");
  } catch (error) {
    console.error("Create error:", error);
    res.status(500).send("Error creating listing");
  }
});

//Edit Route
app.get("/listings/:id/edit", async (req, res) => {
  let { id } = req.params;
  const listing = await Listing.findById(id);
  res.render("listings/edit.ejs", { listing });
});

//Update Route
app.put("/listings/:id", async (req, res) => {
  try {
    let { id } = req.params;
    console.log("Update request for listing:", id);
    console.log("Update data:", req.body.listing);

    const updatedListing = await Listing.findByIdAndUpdate(id, { ...req.body.listing }, { new: true });
    console.log("Updated listing:", updatedListing);

    res.redirect(`/listings/${id}`);
  } catch (error) {
    console.error("Update error:", error);
    res.status(500).send("Error updating listing");
  }
});

//Delete Route
app.delete("/listings/:id", async (req, res) => {
  try {
    let { id } = req.params;
    console.log("Delete request for listing:", id);
    let deletedListing = await Listing.findByIdAndDelete(id);
    console.log("Deleted listing:", deletedListing);
    res.redirect("/listings");
  } catch (error) {
    console.error("Delete error:", error);
    res.status(500).send("Error deleting listing");
  }
});

// app.get("/testListing", async (req, res) => {
//   let sampleListing = new Listing({
//     title: "My New Villa",
//     description: "By the beach",
//     price: 1200,
//     location: "Calangute, Goa",
//     country: "India",
//   });

//   await sampleListing.save();
//   console.log("sample was saved");
//   res.send("successful testing");
// });

app.listen(8080, () => {
  console.log("server is listening to port 8080");
});
