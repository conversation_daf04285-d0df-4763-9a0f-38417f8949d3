/* Custom CSS for Wanderlust - Modern Travel Listing App */

:root {
  --primary-color: #ff385c;
  --primary-dark: #d50027;
  --secondary-color: #00a699;
  --accent-color: #ffc107;
  --text-dark: #222222;
  --text-light: #717171;
  --border-light: #dddddd;
  --background-light: #f7f7f7;
  --white: #ffffff;
  --shadow: 0 2px 16px rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 6px 20px rgba(0, 0, 0, 0.15);
  --border-radius: 12px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--white);
}

/* Navigation */
.navbar {
  background-color: var(--white) !important;
  border-bottom: 1px solid var(--border-light);
  padding: 1rem 0;
  box-shadow: var(--shadow);
}

.navbar-brand {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color) !important;
  text-decoration: none;
}

.navbar-brand:hover {
  color: var(--primary-dark) !important;
}

.nav-link {
  color: var(--text-dark) !important;
  font-weight: 500;
  margin: 0 0.5rem;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--primary-color) !important;
}

/* Buttons */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: var(--border-radius);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: var(--border-radius);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--white);
  padding: 4rem 0;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Search Bar */
.search-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 1rem;
  box-shadow: var(--shadow);
  margin: 2rem 0;
}

.search-input {
  border: 2px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(255, 56, 92, 0.25);
}

/* Cards */
.listing-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  border: none;
}

.listing-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.listing-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.listing-card:hover .listing-image {
  transform: scale(1.05);
}

.card-body {
  padding: 1.5rem;
}

.listing-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.listing-location {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.listing-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Forms */
.form-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  margin: 2rem 0;
}

.form-label {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.form-control {
  border: 2px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(255, 56, 92, 0.25);
}

/* Footer */
.footer {
  background-color: var(--text-dark);
  color: var(--white);
  padding: 3rem 0 1rem;
  margin-top: 4rem;
}

.footer h5 {
  color: var(--white);
  margin-bottom: 1rem;
}

.footer a {
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .listing-image {
    height: 200px;
  }
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 56, 92, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary-custom {
  color: var(--primary-color) !important;
}

.bg-light-custom {
  background-color: var(--background-light) !important;
}

.shadow-custom {
  box-shadow: var(--shadow) !important;
}

.border-radius-custom {
  border-radius: var(--border-radius) !important;
}

/* Category Filters */
.category-filters {
  position: relative;
}

.category-scroll-container {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  flex: 1;
}

.category-scroll-container::-webkit-scrollbar {
  display: none;
}

.category-filters-wrapper {
  gap: 1rem;
  padding: 0.5rem 0;
  min-width: max-content;
}

.category-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 80px;
  color: var(--text-light);
  text-decoration: none;
  position: relative;
  white-space: nowrap;
}

.category-btn i {
  font-size: 1.5rem;
}

.category-btn span {
  font-size: 0.85rem;
  font-weight: 500;
}

.category-btn:hover {
  color: var(--text-dark);
  background-color: var(--background-light);
}

.category-btn.active {
  color: var(--text-dark);
  font-weight: 600;
}

.category-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background-color: var(--text-dark);
  border-radius: 1px;
}

.filter-toggle {
  margin-left: 1rem;
}

.filter-toggle .btn {
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  white-space: nowrap;
}

/* Responsive category filters */
@media (max-width: 768px) {
  .category-filters-wrapper {
    gap: 0.5rem;
  }

  .category-btn {
    min-width: 70px;
    padding: 0.75rem 1rem;
  }

  .category-btn i {
    font-size: 1.25rem;
  }

  .category-btn span {
    font-size: 0.75rem;
  }
}

/* Amenity Items */
.amenity-item {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.amenity-item:hover {
  background-color: var(--white) !important;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.amenity-item i {
  font-size: 1.1rem;
}

/* Enhanced Cards */
.card.shadow-custom {
  transition: all 0.3s ease;
}

.card.shadow-custom:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

/* Booking Sidebar */
.booking-sidebar {
  position: relative;
}

.booking-sidebar .sticky-top {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .booking-sidebar .sticky-top {
    position: relative !important;
    top: auto !important;
    max-height: none;
  }
}

/* Modal improvements */
.modal-content {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--shadow-hover);
}

.modal-header {
  border-bottom: 1px solid var(--border-light);
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid var(--border-light);
  padding: 1.5rem;
}
