<!-- Simple Clean Authentication Styles -->
<style>
.auth-wrapper {
    min-height: 100vh;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.auth-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 420px;
    width: 100%;
    margin: 0 1rem;
    padding: 2.5rem;
    border: 1px solid #e9ecef;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #ff385c;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.auth-logo i {
    font-size: 1.5rem;
    color: white;
}

.auth-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #ff385c;
    box-shadow: 0 0 0 3px rgba(255, 56, 92, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

.btn-primary-custom {
    width: 100%;
    padding: 0.875rem;
    background: #ff385c;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    margin-bottom: 1.5rem;
}

.btn-primary-custom:hover {
    background: #e91e63;
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-row .form-group {
    flex: 1;
}

.divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
}

.divider span {
    padding: 0 1rem;
    color: #9ca3af;
    font-size: 0.9rem;
    font-weight: 500;
}

.social-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    flex: 1;
    padding: 0.875rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: #374151;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.google-btn:hover { border-color: #4285f4; background: rgba(66, 133, 244, 0.05); }
.facebook-btn:hover { border-color: #1877f2; background: rgba(24, 119, 242, 0.05); }

.form-check {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.form-check-input {
    margin-right: 0.75rem;
    margin-top: 0.25rem;
    accent-color: #667eea;
}

.form-check-label {
    font-size: 0.9rem;
    color: #6b7280;
    line-height: 1.5;
}

.form-check-label a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.form-check-label a:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.auth-footer a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-container {
        padding: 2rem 1.5rem;
    }

    .form-group.row {
        flex-direction: column;
    }

    .form-group.row .col {
        margin-bottom: 1rem;
    }
}
</style>

<div class="auth-wrapper">
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="bi bi-house-heart"></i>
            </div>
            <h1 class="auth-title">Create Account</h1>
            <p class="auth-subtitle">Join Quick Stay and start your journey</p>
        </div>

        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <form action="/auth/signup" method="POST" class="needs-validation" novalidate>
            <div class="form-row">
                <div class="form-group">
                    <input type="text" class="form-input" id="firstName" name="firstName"
                           placeholder="First Name" required>
                    <div class="invalid-feedback">Please provide your first name.</div>
                </div>
                <div class="form-group">
                    <input type="text" class="form-input" id="lastName" name="lastName"
                           placeholder="Last Name" required>
                    <div class="invalid-feedback">Please provide your last name.</div>
                </div>
            </div>

            <div class="form-group">
                <input type="email" class="form-input" id="email" name="email"
                       placeholder="Email Address" required>
                <div class="invalid-feedback">Please provide a valid email address.</div>
            </div>

            <div class="form-group">
                <input type="password" class="form-input" id="password" name="password"
                       placeholder="Password" minlength="6" required>
                <div class="invalid-feedback">Password must be at least 6 characters long.</div>
            </div>

            <div class="form-group">
                <input type="password" class="form-input" id="confirmPassword" name="confirmPassword"
                       placeholder="Confirm Password" required>
                <div class="invalid-feedback">Passwords do not match.</div>
            </div>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="terms" required>
                <label class="form-check-label" for="terms" style="font-size: 0.9rem;">
                    I agree to the <a href="#" style="color: #ff385c;">Terms of Service</a> and
                    <a href="#" style="color: #ff385c;">Privacy Policy</a>
                </label>
            </div>

            <button type="submit" class="btn-primary-custom">
                Create Account
            </button>
        </form>

        <div class="auth-footer">
            <p class="text-muted">
                Already have an account?
                <a href="/auth/signin">Sign In</a>
            </p>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
