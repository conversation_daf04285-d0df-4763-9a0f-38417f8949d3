<!-- Authentication Page Styles -->
<style>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    margin: 0 1rem;
}

.auth-left {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.auth-right {
    padding: 3rem;
}

.auth-logo {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.auth-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    height: auto;
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 56, 92, 0.25);
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

.btn-auth {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 1.5rem;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 56, 92, 0.3);
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.social-btn {
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 0.75rem;
    margin: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #495057;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 50px;
}

.social-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .auth-left {
        padding: 2rem;
    }
    .auth-right {
        padding: 2rem;
    }
}
</style>

<div class="auth-container">
    <div class="auth-card">
        <div class="row g-0">
            <!-- Left Side - Branding -->
            <div class="col-lg-5 auth-left">
                <div>
                    <div class="auth-logo">
                        <i class="bi bi-house-heart"></i> Quick Stay
                    </div>
                    <p class="auth-subtitle">Join millions of travelers finding their perfect stay</p>
                    
                    <div class="features-list">
                        <div class="feature-item mb-3">
                            <i class="bi bi-check-circle me-2"></i>
                            <span>Book unique accommodations</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="bi bi-check-circle me-2"></i>
                            <span>Connect with verified hosts</span>
                        </div>
                        <div class="feature-item mb-3">
                            <i class="bi bi-check-circle me-2"></i>
                            <span>Secure payment protection</span>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-check-circle me-2"></i>
                            <span>24/7 customer support</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Form -->
            <div class="col-lg-7 auth-right">
                <div class="text-center mb-4">
                    <h2 class="fw-bold text-dark">Create Account</h2>
                    <p class="text-muted">Start your journey with Quick Stay</p>
                </div>

                <% if (typeof error !== 'undefined' && error) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>

                <form action="/auth/signup" method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="firstName" name="firstName" 
                                       placeholder="First Name" required>
                                <label for="firstName">First Name</label>
                                <div class="invalid-feedback">Please provide your first name.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="lastName" name="lastName" 
                                       placeholder="Last Name" required>
                                <label for="lastName">Last Name</label>
                                <div class="invalid-feedback">Please provide your last name.</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="Email Address" required>
                        <label for="email">Email Address</label>
                        <div class="invalid-feedback">Please provide a valid email address.</div>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Password" minlength="6" required>
                        <label for="password">Password</label>
                        <div class="invalid-feedback">Password must be at least 6 characters long.</div>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                               placeholder="Confirm Password" required>
                        <label for="confirmPassword">Confirm Password</label>
                        <div class="invalid-feedback">Passwords do not match.</div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the <a href="#" class="text-primary">Terms of Service</a> and 
                            <a href="#" class="text-primary">Privacy Policy</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-auth text-white">
                        <i class="bi bi-person-plus me-2"></i>Create Account
                    </button>
                </form>

                <div class="divider">
                    <span>or continue with</span>
                </div>

                <div class="text-center mb-4">
                    <a href="#" class="social-btn">
                        <i class="bi bi-google"></i>
                    </a>
                    <a href="#" class="social-btn">
                        <i class="bi bi-facebook"></i>
                    </a>
                    <a href="#" class="social-btn">
                        <i class="bi bi-apple"></i>
                    </a>
                </div>

                <div class="text-center">
                    <p class="text-muted">
                        Already have an account? 
                        <a href="/auth/signin" class="text-primary fw-semibold">Sign In</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
