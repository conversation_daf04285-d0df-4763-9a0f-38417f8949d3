<!-- Modern Authentication Styles -->
<style>
.auth-wrapper {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.auth-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.auth-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 100%;
    margin: 0 1rem;
    padding: 3rem 2.5rem;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.auth-logo i {
    font-size: 2rem;
    color: white;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 1rem;
    font-weight: 400;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

.form-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.form-input:focus + .form-icon {
    color: #667eea;
}

.btn-primary-custom {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 16px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.btn-primary-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary-custom:hover::before {
    left: 100%;
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
}

.divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
}

.divider span {
    padding: 0 1rem;
    color: #9ca3af;
    font-size: 0.9rem;
    font-weight: 500;
}

.social-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    flex: 1;
    padding: 0.875rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: #374151;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.google-btn:hover { border-color: #4285f4; background: rgba(66, 133, 244, 0.05); }
.facebook-btn:hover { border-color: #1877f2; background: rgba(24, 119, 242, 0.05); }

.form-check {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.form-check-input {
    margin-right: 0.75rem;
    margin-top: 0.25rem;
    accent-color: #667eea;
}

.form-check-label {
    font-size: 0.9rem;
    color: #6b7280;
    line-height: 1.5;
}

.form-check-label a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.form-check-label a:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.auth-footer a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-container {
        padding: 2rem 1.5rem;
    }

    .form-group.row {
        flex-direction: column;
    }

    .form-group.row .col {
        margin-bottom: 1rem;
    }
}
</style>

<div class="auth-wrapper">
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="bi bi-house-heart"></i>
            </div>
            <h1 class="auth-title">Create Account</h1>
            <p class="auth-subtitle">Join Quick Stay and start your journey</p>
        </div>

        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <form action="/auth/signup" method="POST" class="needs-validation" novalidate>
            <div class="form-group row d-flex gap-3">
                <div class="col">
                    <input type="text" class="form-input" id="firstName" name="firstName"
                           placeholder="First Name" required>
                    <i class="bi bi-person form-icon"></i>
                    <div class="invalid-feedback">Please provide your first name.</div>
                </div>
                <div class="col">
                    <input type="text" class="form-input" id="lastName" name="lastName"
                           placeholder="Last Name" required>
                    <i class="bi bi-person form-icon"></i>
                    <div class="invalid-feedback">Please provide your last name.</div>
                </div>
            </div>

            <div class="form-group">
                <input type="email" class="form-input" id="email" name="email"
                       placeholder="Email Address" required>
                <i class="bi bi-envelope form-icon"></i>
                <div class="invalid-feedback">Please provide a valid email address.</div>
            </div>

            <div class="form-group">
                <input type="password" class="form-input" id="password" name="password"
                       placeholder="Password" minlength="6" required>
                <i class="bi bi-lock form-icon"></i>
                <div class="invalid-feedback">Password must be at least 6 characters long.</div>
            </div>

            <div class="form-group">
                <input type="password" class="form-input" id="confirmPassword" name="confirmPassword"
                       placeholder="Confirm Password" required>
                <i class="bi bi-lock form-icon"></i>
                <div class="invalid-feedback">Passwords do not match.</div>
            </div>

            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="terms" required>
                <label class="form-check-label" for="terms">
                    I agree to the <a href="#">Terms of Service</a> and
                    <a href="#">Privacy Policy</a>
                </label>
            </div>

            <button type="submit" class="btn-primary-custom">
                <i class="bi bi-person-plus me-2"></i>Create Account
            </button>
        </form>

        <div class="divider">
            <span>or continue with</span>
        </div>

        <div class="social-buttons">
            <a href="#" class="social-btn google-btn">
                <i class="bi bi-google"></i>
            </a>
            <a href="#" class="social-btn facebook-btn">
                <i class="bi bi-facebook"></i>
            </a>
        </div>

        <div class="auth-footer">
            <p class="text-muted">
                Already have an account?
                <a href="/auth/signin">Sign In</a>
            </p>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
