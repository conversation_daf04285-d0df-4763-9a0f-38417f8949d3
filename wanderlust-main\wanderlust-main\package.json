{"name": "majorproject", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.13.1", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "ejs": "^3.1.9", "express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-session": "^1.18.1", "method-override": "^3.0.0", "mongoose": "^7.5.2", "passport": "^0.7.0", "passport-local": "^1.0.0"}}