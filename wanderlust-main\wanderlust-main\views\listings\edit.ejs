<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light-custom py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="/listings">All Listings</a></li>
            <li class="breadcrumb-item"><a href="/listings/<%= listing._id %>"><%= listing.title %></a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </ol>
    </div>
</nav>

<!-- Main Content -->
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="h2 mb-3">Edit Your Listing</h1>
                <p class="text-muted">Update your property information</p>
            </div>

            <!-- Current Image Preview -->
            <div class="card border-0 bg-light-custom mb-4">
                <div class="card-body text-center">
                    <h6 class="card-title">Current Property Image</h6>
                    <img src="<%= listing.image %>" class="img-fluid rounded shadow-custom"
                         style="max-height: 200px;" alt="<%= listing.title %>">
                </div>
            </div>

            <!-- Form -->
            <div class="form-container">
                <form method="POST" action="/listings/<%= listing._id %>?_method=PUT" class="needs-validation" novalidate>
                    <!-- Step 1: Basic Information -->
                    <div class="mb-5">
                        <h4 class="mb-4">
                            <span class="badge bg-primary me-2">1</span>
                            Basic Information
                        </h4>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">Property Title *</label>
                                <input type="text" class="form-control" id="title" name="listing[title]"
                                       value="<%= listing.title %>" required>
                                <div class="invalid-feedback">
                                    Please provide a property title.
                                </div>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control" id="description" name="listing[description]"
                                          rows="4" maxlength="500" required><%= listing.description %></textarea>
                                <div class="invalid-feedback">
                                    Please provide a description.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Location -->
                    <div class="mb-5">
                        <h4 class="mb-4">
                            <span class="badge bg-primary me-2">2</span>
                            Location Details
                        </h4>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country *</label>
                                <input type="text" class="form-control" id="country" name="listing[country]"
                                       value="<%= listing.country %>" required>
                                <div class="invalid-feedback">
                                    Please provide a country.
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">City/Location *</label>
                                <input type="text" class="form-control" id="location" name="listing[location]"
                                       value="<%= listing.location %>" required>
                                <div class="invalid-feedback">
                                    Please provide a location.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Pricing & Images -->
                    <div class="mb-5">
                        <h4 class="mb-4">
                            <span class="badge bg-primary me-2">3</span>
                            Pricing & Images
                        </h4>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="price" class="form-label">Price per Night (INR) *</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="price" name="listing[price]"
                                           value="<%= listing.price %>" min="1" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="image" class="form-label">Image URL</label>
                                <input type="url" class="form-control" id="image" name="listing[image]"
                                       value="<%= listing.image %>">
                                <div class="form-text">Update to change the property image</div>
                            </div>
                        </div>

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3 text-center"></div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="/listings/<%= listing._id %>" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Update Listing
                        </button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="card border-0 bg-light-custom mt-5">
                <div class="card-body text-center">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Make sure all information is accurate before updating your listing
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
