<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="hero-title">Find Your Perfect Stay</h1>
                <p class="hero-subtitle">Discover unique accommodations around the world</p>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="row g-3 align-items-center">
                        <div class="col-md-8">
                            <input type="text" class="form-control search-input" id="searchInput"
                                   placeholder="Search by destination or property name...">
                        </div>
                        <div class="col-md-4">
                            <a href="/listings/new" class="btn btn-primary w-100">
                                <i class="bi bi-plus-circle me-2"></i>Add Your Property
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Category Filter Section -->
<section class="py-4 bg-white border-bottom">
    <div class="container">
        <div class="category-filters">
            <div class="d-flex align-items-center justify-content-between mb-3">
                <div class="category-scroll-container">
                    <div class="category-filters-wrapper d-flex">
                        <button class="category-btn active" data-category="all">
                            <i class="bi bi-grid"></i>
                            <span>All</span>
                        </button>
                        <button class="category-btn" data-category="Trending">
                            <i class="bi bi-fire"></i>
                            <span>Trending</span>
                        </button>
                        <button class="category-btn" data-category="Rooms">
                            <i class="bi bi-door-open"></i>
                            <span>Rooms</span>
                        </button>
                        <button class="category-btn" data-category="Iconic Cities">
                            <i class="bi bi-building"></i>
                            <span>Iconic Cities</span>
                        </button>
                        <button class="category-btn" data-category="Mountains">
                            <i class="bi bi-triangle"></i>
                            <span>Mountains</span>
                        </button>
                        <button class="category-btn" data-category="Castles">
                            <i class="bi bi-bank"></i>
                            <span>Castles</span>
                        </button>
                        <button class="category-btn" data-category="Amazing Pools">
                            <i class="bi bi-droplet"></i>
                            <span>Amazing Pools</span>
                        </button>
                        <button class="category-btn" data-category="Camping">
                            <i class="bi bi-tree-fill"></i>
                            <span>Camping</span>
                        </button>
                        <button class="category-btn" data-category="Farms">
                            <i class="bi bi-flower2"></i>
                            <span>Farms</span>
                        </button>
                        <button class="category-btn" data-category="Arctic">
                            <i class="bi bi-snow2"></i>
                            <span>Arctic</span>
                        </button>
                        <button class="category-btn" data-category="Domes">
                            <i class="bi bi-circle-fill"></i>
                            <span>Domes</span>
                        </button>
                        <button class="category-btn" data-category="Boats">
                            <i class="bi bi-water"></i>
                            <span>Boats</span>
                        </button>
                    </div>
                </div>
                <div class="filter-toggle">
                    <button class="btn btn-outline-secondary" id="filterToggle">
                        <i class="bi bi-sliders"></i> Filters
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Listings Section -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-center mb-4">Explore Amazing Places</h2>
                <p class="text-center text-muted mb-5">Choose from over <%= allListings.length %> unique properties worldwide</p>
            </div>
        </div>

        <% if (allListings.length === 0) { %>
            <div class="row justify-content-center">
                <div class="col-md-6 text-center">
                    <div class="card shadow-custom border-0 p-5">
                        <i class="bi bi-house-door display-1 text-muted mb-3"></i>
                        <h4>No Listings Yet</h4>
                        <p class="text-muted mb-4">Be the first to add a property to our platform!</p>
                        <a href="/listings/new" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Add First Listing
                        </a>
                    </div>
                </div>
            </div>
        <% } else { %>
            <div class="row" id="listingsContainer">
                <% for(let listing of allListings) { %>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card listing-card h-100" data-category="<%= listing.category || 'Trending' %>">
                            <div class="position-relative">
                                <img src="<%= listing.image %>" class="listing-image" alt="<%= listing.title %>">
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-primary"><%= listing.category || 'Featured' %></span>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h5 class="listing-title"><%= listing.title %></h5>
                                <p class="listing-location">
                                    <i class="bi bi-geo-alt text-primary"></i>
                                    <%= listing.location %>, <%= listing.country %>
                                </p>

                                <p class="card-text text-muted flex-grow-1">
                                    <%= listing.description ? listing.description.substring(0, 100) + '...' : 'Beautiful property with amazing amenities.' %>
                                </p>

                                <div class="d-flex justify-content-between align-items-center mt-auto">
                                    <div class="listing-price">
                                        ₹<%= listing.price ? listing.price.toLocaleString('en-IN') : '0' %> <small class="text-muted">/ night</small>
                                    </div>
                                    <a href="/listings/<%= listing._id %>" class="btn btn-outline-primary btn-sm">
                                        View Details <i class="bi bi-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>
        <% } %>
    </div>
</section>
