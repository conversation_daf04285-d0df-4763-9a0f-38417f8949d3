<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light-custom py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="/listings">All Listings</a></li>
            <li class="breadcrumb-item active" aria-current="page"><%= listing.title %></li>
        </ol>
    </div>
</nav>

<!-- Main Content -->
<div class="container my-5">
    <div class="row">
        <!-- Image and Gallery -->
        <div class="col-lg-8">
            <div class="card shadow-custom border-0 mb-4">
                <div class="position-relative">
                    <img src="<%= listing.image %>" class="card-img-top" style="height: 400px; object-fit: cover;" alt="<%= listing.title %>">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-primary fs-6">Featured</span>
                    </div>
                </div>
            </div>

            <!-- Property Details -->
            <div class="card shadow-custom border-0 mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h1 class="h2 mb-2"><%= listing.title %></h1>
                            <p class="text-muted mb-0">
                                <i class="bi bi-geo-alt text-primary"></i>
                                <%= listing.location %>, <%= listing.country %>
                            </p>
                        </div>
                        <div class="text-end">
                            <div class="h3 text-primary mb-0">₹<%= listing.price ? listing.price.toLocaleString('en-IN') : '0' %></div>
                            <small class="text-muted">per night</small>
                        </div>
                    </div>

                    <hr>

                    <h5 class="mb-3">About this place</h5>
                    <p class="text-muted">
                        <%= listing.description || 'This beautiful property offers a unique experience with modern amenities and stunning views. Perfect for travelers looking for comfort and style.' %>
                    </p>

                    <!-- Property Details -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-people text-primary me-2"></i>
                                <span><%= listing.maxGuests || 4 %> guests</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-door-open text-primary me-2"></i>
                                <span><%= listing.bedrooms || 2 %> bedrooms</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-droplet text-primary me-2"></i>
                                <span><%= listing.bathrooms || 1 %> bathrooms</span>
                            </div>
                        </div>
                    </div>

                    <!-- Amenities -->
                    <h5 class="mb-3 mt-4">What this place offers</h5>
                    <% if (listing.amenities && listing.amenities.length > 0) { %>
                        <div class="row">
                            <% listing.amenities.forEach((amenity, index) => { %>
                                <% if (index % 2 === 0) { %>
                                    <div class="col-md-6">
                                        <div class="amenity-item mb-3 p-3 bg-light rounded">
                                            <i class="bi bi-check-circle text-success me-2"></i>
                                            <span><%= amenity %></span>
                                        </div>
                                    </div>
                                <% } else { %>
                                    <div class="col-md-6">
                                        <div class="amenity-item mb-3 p-3 bg-light rounded">
                                            <i class="bi bi-check-circle text-success me-2"></i>
                                            <span><%= amenity %></span>
                                        </div>
                                    </div>
                                <% } %>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="amenity-item mb-3 p-3 bg-light rounded">
                                    <i class="bi bi-wifi text-primary me-2"></i>
                                    <span>Free WiFi</span>
                                </div>
                                <div class="amenity-item mb-3 p-3 bg-light rounded">
                                    <i class="bi bi-car-front text-primary me-2"></i>
                                    <span>Free parking</span>
                                </div>
                                <div class="amenity-item mb-3 p-3 bg-light rounded">
                                    <i class="bi bi-tv text-primary me-2"></i>
                                    <span>TV</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="amenity-item mb-3 p-3 bg-light rounded">
                                    <i class="bi bi-cup-hot text-primary me-2"></i>
                                    <span>Kitchen</span>
                                </div>
                                <div class="amenity-item mb-3 p-3 bg-light rounded">
                                    <i class="bi bi-snow text-primary me-2"></i>
                                    <span>Air conditioning</span>
                                </div>
                                <div class="amenity-item mb-3 p-3 bg-light rounded">
                                    <i class="bi bi-shield-check text-primary me-2"></i>
                                    <span>Safe</span>
                                </div>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Location Map -->
            <% if (listing.latitude && listing.longitude) { %>
            <div class="card shadow-custom border-0 mb-4">
                <div class="card-body">
                    <h5 class="mb-3">Location</h5>
                    <div id="map" style="height: 300px; border-radius: 12px;"></div>
                    <p class="text-muted mt-2 mb-0">
                        <i class="bi bi-geo-alt me-1"></i>
                        Exact location will be provided after booking confirmation
                    </p>
                </div>
            </div>
            <% } %>
        </div>

        <!-- Booking Card -->
        <div class="col-lg-4">
            <div class="booking-sidebar">
                <div class="card shadow-custom border-0 sticky-top" style="top: 100px; z-index: 10;">
                    <div class="card-body">
                    <div class="text-center mb-4">
                        <h4 class="text-primary">₹<%= listing.price ? listing.price.toLocaleString('en-IN') : '0' %> <small class="text-muted">/ night</small></h4>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-warning me-1">
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-fill"></i>
                                <i class="bi bi-star-half"></i>
                            </span>
                            <small class="text-muted">(4.8) · 24 reviews</small>
                        </div>
                    </div>

                    <!-- Booking Form -->
                    <form class="mb-3">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">Check-in</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Check-out</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Guests</label>
                            <select class="form-select">
                                <option>1 guest</option>
                                <option>2 guests</option>
                                <option>3 guests</option>
                                <option>4 guests</option>
                                <option>5+ guests</option>
                            </select>
                        </div>

                        <button type="button" class="btn btn-primary w-100 mb-3">
                            <i class="bi bi-calendar-check me-2"></i>Reserve
                        </button>
                    </form>

                    <p class="text-center text-muted small mb-3">You won't be charged yet</p>

                    <!-- Price Breakdown -->
                    <div class="border-top pt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>₹<%= listing.price ? listing.price.toLocaleString('en-IN') : '0' %> × 5 nights</span>
                            <span>₹<%= listing.price ? (listing.price * 5).toLocaleString('en-IN') : '0' %></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Service fee</span>
                            <span>₹<%= listing.price ? Math.round(listing.price * 0.1).toLocaleString('en-IN') : '0' %></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total</span>
                            <span>₹<%= listing.price ? (listing.price * 5 + Math.round(listing.price * 0.1)).toLocaleString('en-IN') : '0' %></span>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Action Buttons -->
                <div class="card shadow-custom border-0 mt-4" style="position: relative; z-index: 15;">
                    <div class="card-body">
                        <h6 class="card-title">Manage this listing</h6>
                        <div class="d-grid gap-2">
                            <a href="/listings/<%= listing._id %>/edit" class="btn btn-outline-primary">
                                <i class="bi bi-pencil me-2"></i>Edit Listing
                            </a>
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="bi bi-trash me-2"></i>Delete Listing
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete "<%= listing.title %>"?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="/listings/<%=listing._id%>?_method=DELETE" class="d-inline">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>Delete Listing
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap"></script>

<script>
function initMap() {
    <% if (listing.latitude && listing.longitude) { %>
    const propertyLocation = { lat: <%= listing.latitude %>, lng: <%= listing.longitude %> };

    const map = new google.maps.Map(document.getElementById("map"), {
        zoom: 13,
        center: propertyLocation,
        styles: [
            {
                "featureType": "all",
                "elementType": "geometry.fill",
                "stylers": [{"weight": "2.00"}]
            },
            {
                "featureType": "all",
                "elementType": "geometry.stroke",
                "stylers": [{"color": "#9c9c9c"}]
            },
            {
                "featureType": "all",
                "elementType": "labels.text",
                "stylers": [{"visibility": "on"}]
            }
        ]
    });

    const marker = new google.maps.Marker({
        position: propertyLocation,
        map: map,
        title: "<%= listing.title %>",
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="18" fill="#ff385c" stroke="white" stroke-width="3"/>
                    <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-weight="bold">🏠</text>
                </svg>
            `),
            scaledSize: new google.maps.Size(40, 40)
        }
    });

    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px; max-width: 200px;">
                <h6 style="margin: 0 0 5px 0; color: #222;">${"<%= listing.title %>"}</h6>
                <p style="margin: 0; color: #717171; font-size: 14px;">${"<%= listing.location %>, <%= listing.country %>"}</p>
                <p style="margin: 5px 0 0 0; color: #ff385c; font-weight: bold;">₹${"<%= listing.price ? listing.price.toLocaleString('en-IN') : '0' %>"}/night</p>
            </div>
        `
    });

    marker.addListener("click", () => {
        infoWindow.open(map, marker);
    });
    <% } %>
}

// Fallback if Google Maps fails to load
window.addEventListener('load', function() {
    setTimeout(function() {
        if (typeof google === 'undefined') {
            const mapElement = document.getElementById('map');
            if (mapElement) {
                mapElement.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100 bg-light rounded">
                        <div class="text-center">
                            <i class="bi bi-geo-alt display-4 text-muted"></i>
                            <p class="text-muted mt-2">Map unavailable</p>
                            <small class="text-muted">Location: <%= listing.location %>, <%= listing.country %></small>
                        </div>
                    </div>
                `;
            }
        }
    }, 3000);
});
</script>
