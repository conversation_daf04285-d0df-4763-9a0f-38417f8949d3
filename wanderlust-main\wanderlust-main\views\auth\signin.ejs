<!-- Simple Clean Authentication Styles -->
<style>
.auth-wrapper {
    min-height: 100vh;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.auth-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 400px;
    width: 100%;
    margin: 0 1rem;
    padding: 2.5rem;
    border: 1px solid #e9ecef;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #ff385c;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.auth-logo i {
    font-size: 1.5rem;
    color: white;
}

.auth-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #ff385c;
    box-shadow: 0 0 0 3px rgba(255, 56, 92, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

.btn-primary-custom {
    width: 100%;
    padding: 0.875rem;
    background: #ff385c;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    margin-bottom: 1.5rem;
}

.btn-primary-custom:hover {
    background: #e91e63;
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.form-check {
    display: flex;
    align-items: center;
}

.form-check-input {
    margin-right: 0.5rem;
    accent-color: #ff385c;
}

.forgot-link {
    color: #ff385c;
    text-decoration: none;
}

.forgot-link:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #ff385c;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.forgot-password {
    text-align: right;
    margin-bottom: 1.5rem;
}

.forgot-password a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-left {
        padding: 2rem;
    }
    .auth-right {
        padding: 2rem;
    }
}
</style>

<div class="auth-wrapper">
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="bi bi-house-heart"></i>
            </div>
            <h1 class="auth-title">Welcome Back</h1>
            <p class="auth-subtitle">Sign in to your Quick Stay account</p>
        </div>
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <% if (typeof success !== 'undefined' && success) { %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <form action="/auth/signin" method="POST" class="needs-validation" novalidate>
            <div class="form-group">
                <input type="email" class="form-input" id="email" name="email"
                       placeholder="Email Address" required>
                <div class="invalid-feedback">Please provide a valid email address.</div>
            </div>

            <div class="form-group">
                <input type="password" class="form-input" id="password" name="password"
                       placeholder="Password" required>
                <div class="invalid-feedback">Please provide your password.</div>
            </div>

            <div class="remember-forgot">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">Remember me</label>
                </div>
                <a href="/auth/forgot-password" class="forgot-link">Forgot Password?</a>
            </div>

            <button type="submit" class="btn-primary-custom">
                Sign In
            </button>
        </form>

        <div class="auth-footer">
            <p class="text-muted">
                Don't have an account?
                <a href="/auth/signup">Create Account</a>
            </p>
        </div>
    </div>
</div>
