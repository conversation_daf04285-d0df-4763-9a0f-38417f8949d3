<!-- Authentication Page Styles -->
<style>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 800px;
    width: 100%;
    margin: 0 1rem;
}

.auth-left {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.auth-right {
    padding: 3rem;
}

.auth-logo {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.auth-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    height: auto;
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 56, 92, 0.25);
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

.btn-auth {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 1.5rem;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 56, 92, 0.3);
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.social-btn {
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 0.75rem;
    margin: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #495057;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 50px;
}

.social-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.forgot-password {
    text-align: right;
    margin-bottom: 1.5rem;
}

.forgot-password a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .auth-left {
        padding: 2rem;
    }
    .auth-right {
        padding: 2rem;
    }
}
</style>

<div class="auth-container">
    <div class="auth-card">
        <div class="row g-0">
            <!-- Left Side - Branding -->
            <div class="col-lg-5 auth-left">
                <div>
                    <div class="auth-logo">
                        <i class="bi bi-house-heart"></i> Quick Stay
                    </div>
                    <p class="auth-subtitle">Welcome back! Sign in to continue your journey</p>
                    
                    <div class="welcome-stats">
                        <div class="stat-item mb-3">
                            <div class="h4 mb-1">2M+</div>
                            <small>Happy Travelers</small>
                        </div>
                        <div class="stat-item mb-3">
                            <div class="h4 mb-1">50K+</div>
                            <small>Unique Properties</small>
                        </div>
                        <div class="stat-item">
                            <div class="h4 mb-1">190+</div>
                            <small>Countries</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Form -->
            <div class="col-lg-7 auth-right">
                <div class="text-center mb-4">
                    <h2 class="fw-bold text-dark">Welcome Back</h2>
                    <p class="text-muted">Sign in to your account</p>
                </div>

                <% if (typeof error !== 'undefined' && error) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>

                <% if (typeof success !== 'undefined' && success) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%= success %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>

                <form action="/auth/signin" method="POST" class="needs-validation" novalidate>
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="Email Address" required>
                        <label for="email">Email Address</label>
                        <div class="invalid-feedback">Please provide a valid email address.</div>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Password" required>
                        <label for="password">Password</label>
                        <div class="invalid-feedback">Please provide your password.</div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                        <div class="forgot-password">
                            <a href="/auth/forgot-password">Forgot Password?</a>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-auth text-white">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                    </button>
                </form>

                <div class="divider">
                    <span>or continue with</span>
                </div>

                <div class="text-center mb-4">
                    <a href="#" class="social-btn">
                        <i class="bi bi-google"></i>
                    </a>
                    <a href="#" class="social-btn">
                        <i class="bi bi-facebook"></i>
                    </a>
                    <a href="#" class="social-btn">
                        <i class="bi bi-apple"></i>
                    </a>
                </div>

                <div class="text-center">
                    <p class="text-muted">
                        Don't have an account? 
                        <a href="/auth/signup" class="text-primary fw-semibold">Sign Up</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
