<!-- Authentication Page Styles -->
<style>
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 500px;
    width: 100%;
    margin: 0 1rem;
}

.auth-content {
    padding: 3rem;
    text-align: center;
}

.auth-logo {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    height: auto;
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 56, 92, 0.25);
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

.btn-auth {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 1.5rem;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 56, 92, 0.3);
}

@media (max-width: 768px) {
    .auth-content {
        padding: 2rem;
    }
}
</style>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-content">
            <div class="auth-logo">
                <i class="bi bi-house-heart"></i> Quick Stay
            </div>
            
            <h2 class="fw-bold text-dark mb-3">Forgot Password?</h2>
            <p class="text-muted mb-4">No worries! Enter your email address and we'll send you a link to reset your password.</p>

            <% if (typeof error !== 'undefined' && error) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <%= error %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% } %>

            <% if (typeof success !== 'undefined' && success) { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <%= success %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <% } %>

            <form action="/auth/forgot-password" method="POST" class="needs-validation" novalidate>
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" 
                           placeholder="Email Address" required>
                    <label for="email">Email Address</label>
                    <div class="invalid-feedback">Please provide a valid email address.</div>
                </div>

                <button type="submit" class="btn btn-auth text-white">
                    <i class="bi bi-envelope me-2"></i>Send Reset Link
                </button>
            </form>

            <div class="text-center">
                <p class="text-muted mb-2">
                    Remember your password? 
                    <a href="/auth/signin" class="text-primary fw-semibold">Sign In</a>
                </p>
                <p class="text-muted">
                    Don't have an account? 
                    <a href="/auth/signup" class="text-primary fw-semibold">Sign Up</a>
                </p>
            </div>
        </div>
    </div>
</div>
